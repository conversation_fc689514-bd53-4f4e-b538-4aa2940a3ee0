"""
时间线分析器 - 基于时间线的照片分组和异步hash计算
"""

import asyncio
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Callable
import threading
from queue import Queue

from .models import PhotoInfo, SimilarityGroup
from .optimized_analyzer import OptimizedPhotoAnalyzer, BatchProcessingConfig
from .analyzer import TemporalAnalyzer

logger = logging.getLogger(__name__)


class TimelineGroup:
    """时间线组 - 基于date_added的时间窗口分组"""

    def __init__(self, start_time: datetime, time_threshold_seconds: int = 300):
        self.start_time = start_time
        self.end_time = start_time + timedelta(seconds=time_threshold_seconds)
        self.time_threshold_seconds = time_threshold_seconds
        self.photos: List[PhotoInfo] = []
        self.hash_completed = False
        self.similarity_groups: List[SimilarityGroup] = []

    def can_add_photo(self, photo: PhotoInfo) -> bool:
        """检查照片是否可以添加到此时间线组"""
        photo_added_time = photo.date_taken  # date_taken已经是datetime对象
        return self.start_time <= photo_added_time <= self.end_time

    def add_photo(self, photo: PhotoInfo):
        """添加照片到时间线组"""
        self.photos.append(photo)
        # 动态调整结束时间
        photo_added_time = photo.date_taken  # date_taken已经是datetime对象
        if photo_added_time > self.end_time:
            self.end_time = photo_added_time + timedelta(seconds=self.time_threshold_seconds)

    def should_split(self, next_photo: PhotoInfo) -> bool:
        """判断是否应该拆分组（下一张照片时间间隔超过阈值）"""
        if not self.photos:
            return False

        last_photo_time = self.photos[-1].date_taken  # date_taken已经是datetime对象
        next_photo_time = next_photo.date_taken  # date_taken已经是datetime对象
        time_diff = (next_photo_time - last_photo_time).total_seconds()

        return time_diff > self.time_threshold_seconds


class TimelineAnalyzer:
    """基于时间线的照片分析器"""

    def __init__(self, time_threshold_seconds: int = 300, max_workers: int = 4):
        self.time_threshold_seconds = time_threshold_seconds
        self.max_workers = max_workers
        self.timeline_groups: List[TimelineGroup] = []
        self.hash_queue = Queue()
        self.hash_workers_running = False
        self.hash_executor = ThreadPoolExecutor(max_workers=max_workers)
        self.analyzer_config = BatchProcessingConfig(
            batch_size=50,
            max_workers=max_workers,
            ahash_threshold=0.9,
            phash_threshold=0.85
        )
        # 用于增量分析的状态管理
        self.all_photos: List[PhotoInfo] = []
        self.photo_to_group_map: Dict[str, int] = {}  # photo_id -> group_index

    def create_timeline_groups(self, photos: List[PhotoInfo]) -> List[TimelineGroup]:
        """基于date_added创建时间线组"""
        if not photos:
            return []

        # 按date_taken排序（作为date_added的代理）
        sorted_photos = sorted(photos, key=lambda p: p.date_taken)

        groups = []
        current_group = None

        for photo in sorted_photos:
            photo_time = photo.date_taken  # date_taken已经是datetime对象

            # 如果没有当前组或需要创建新组
            if current_group is None or current_group.should_split(photo):
                current_group = TimelineGroup(photo_time, self.time_threshold_seconds)
                groups.append(current_group)

            current_group.add_photo(photo)

        logger.info(f"创建了 {len(groups)} 个时间线组，时间阈值: {self.time_threshold_seconds}秒")

        # 记录每个组的统计信息
        for i, group in enumerate(groups):
            duration = (group.end_time - group.start_time).total_seconds()
            logger.info(f"时间线组 {i+1}: {len(group.photos)} 张照片, "
                       f"时间跨度: {duration:.1f}秒 "
                       f"({group.start_time.strftime('%Y-%m-%d %H:%M:%S')} - "
                       f"{group.end_time.strftime('%Y-%m-%d %H:%M:%S')})")

        return groups

    def start_async_hash_calculation(self, photos: List[PhotoInfo],
                                   progress_callback: Optional[Callable] = None):
        """启动异步hash计算"""
        if self.hash_workers_running:
            return

        self.hash_workers_running = True
        logger.info(f"启动异步hash计算，处理 {len(photos)} 张照片")

        # 将照片添加到队列
        for photo in photos:
            self.hash_queue.put(photo)

        # 启动hash计算工作线程
        def hash_worker():
            processed = 0
            while self.hash_workers_running:
                try:
                    photo = self.hash_queue.get(timeout=1.0)
                    if photo is None:  # 结束信号
                        break

                    # 计算hash值
                    self._calculate_photo_hashes(photo)
                    processed += 1

                    if progress_callback:
                        progress_callback(processed, len(photos))

                    self.hash_queue.task_done()

                except Exception as e:
                    if "Empty" not in str(e):  # 忽略队列空的异常
                        logger.warning(f"Hash计算异常: {e}")
                    continue

        # 启动多个工作线程
        for _ in range(self.max_workers):
            thread = threading.Thread(target=hash_worker, daemon=True)
            thread.start()

    def _calculate_photo_hashes(self, photo: PhotoInfo):
        """计算单张照片的hash值（只计算优化分析器需要的ahash和phash）"""
        try:
            # 如果已有hash值则跳过
            if photo.hash_values and photo.hash_values.get('ahash') and photo.hash_values.get('phash'):
                return

            # 只计算优化分析器实际使用的hash算法
            hash_values = self._calculate_essential_hashes(photo)

            # 更新照片的hash值到hash_values字典中
            if photo.hash_values is None:
                photo.hash_values = {}

            photo.hash_values.update(hash_values)

            logger.debug(f"完成照片hash计算: {photo.filename}, hash_values: {list(hash_values.keys())}")

        except Exception as e:
            logger.warning(f"计算照片 {photo.filename} hash失败: {e}")

    def _calculate_essential_hashes(self, photo: PhotoInfo) -> Dict[str, str]:
        """只计算优化分析器需要的核心hash算法（ahash和phash）"""
        import imagehash
        from PIL import Image

        hash_values = {}
        image_path = photo.thumbnail_path or photo.original_path

        try:
            with Image.open(image_path) as img:
                if img.mode != "RGB":
                    img = img.convert("RGB")

                # 只计算优化分析器使用的两种hash
                hash_values['ahash'] = str(imagehash.average_hash(img))
                hash_values['phash'] = str(imagehash.phash(img))

                logger.debug(f"计算核心hash完成: {photo.filename}")

        except Exception as e:
            logger.warning(f"计算照片 {photo.filename} 核心hash失败: {e}")

        return hash_values

    def _process_photos_for_frontend(self, photos: List[PhotoInfo]):
        """处理照片数据，确保前端能够正确显示hash值"""
        for photo in photos:
            if photo.hash_values:
                # 将hash_values字典中的值设置为直接属性，以便前端显示
                # 只处理优化分析器实际使用的hash算法
                for hash_type, hash_value in photo.hash_values.items():
                    setattr(photo, hash_type, hash_value)

                logger.info(f"✅ 为照片 {photo.filename} 设置hash属性: {list(photo.hash_values.keys())}")
            else:
                logger.warning(f"⚠️ 照片 {photo.filename} 没有hash_values")

    def stop_async_hash_calculation(self):
        """停止异步hash计算"""
        self.hash_workers_running = False

        # 发送结束信号
        for _ in range(self.max_workers):
            self.hash_queue.put(None)

    def analyze_timeline_group(self, group: TimelineGroup) -> List[SimilarityGroup]:
        """分析单个时间线组的相似性"""
        if len(group.photos) < 2:
            return []

        logger.info(f"分析时间线组: {len(group.photos)} 张照片")

        try:
            # 使用优化分析器分析组内照片
            analyzer = OptimizedPhotoAnalyzer(self.analyzer_config)
            result = analyzer.analyze_photos_optimized(group.photos)

            similarity_groups = result.get('similarity_groups', [])
            group.similarity_groups = similarity_groups
            group.hash_completed = True

            logger.info(f"时间线组分析完成: 找到 {len(similarity_groups)} 个相似组")
            return similarity_groups

        except Exception as e:
            logger.error(f"时间线组分析失败: {e}")
            return []

    def analyze_photos_by_timeline(self, photos: List[PhotoInfo],
                                 progress_callback: Optional[Callable] = None) -> Dict:
        """基于时间线分析照片"""
        start_time = time.time()

        # 1. 创建时间线组
        timeline_groups = self.create_timeline_groups(photos)
        self.timeline_groups = timeline_groups

        # 2. 启动异步hash计算
        self.start_async_hash_calculation(photos, progress_callback)

        # 3. 等待hash计算完成（或超时）
        max_wait_time = 60  # 最多等待60秒
        wait_start = time.time()

        while (time.time() - wait_start) < max_wait_time:
            if self.hash_queue.empty():
                break
            time.sleep(0.5)

        # 4. 分析每个时间线组
        all_similarity_groups = []

        for i, group in enumerate(timeline_groups):
            if progress_callback:
                progress_callback(i, len(timeline_groups))

            group_similarity_groups = self.analyze_timeline_group(group)
            all_similarity_groups.extend(group_similarity_groups)

        # 5. 停止hash计算
        self.stop_async_hash_calculation()

        # 6. 处理所有照片的hash值，确保前端能够正确显示
        self._process_photos_for_frontend(photos)

        # 7. 处理相似组中的照片hash值
        for group in all_similarity_groups:
            if 'photos' in group:
                self._process_photos_for_frontend(group['photos'])

        total_time = time.time() - start_time

        result = {
            'similarity_groups': all_similarity_groups,
            'timeline_groups_count': len(timeline_groups),
            'total_photos': len(photos),
            'total_time_seconds': total_time,
            'performance_stats': {
                'timeline_groups': len(timeline_groups),
                'avg_photos_per_group': len(photos) / len(timeline_groups) if timeline_groups else 0,
                'time_threshold_seconds': self.time_threshold_seconds
            }
        }

        logger.info(f"时间线分析完成: {len(timeline_groups)} 个组, "
                   f"{len(all_similarity_groups)} 个相似组, 耗时 {total_time:.2f}秒")

        return result

    def add_photos_incrementally(self, new_photos: List[PhotoInfo]) -> Dict:
        """
        增量添加新照片并重新分析受影响的时间线组

        Args:
            new_photos: 新添加的照片列表

        Returns:
            分析结果字典，包含更新的相似组
        """
        start_time = time.time()
        logger.info(f"🔄 开始增量添加 {len(new_photos)} 张新照片")

        # 1. 为新照片计算hash值
        self.start_async_hash_calculation(new_photos)

        # 等待hash计算完成
        max_wait_time = 30
        wait_start = time.time()
        while (time.time() - wait_start) < max_wait_time:
            if self.hash_queue.empty():
                break
            time.sleep(0.1)

        # 2. 将新照片分配到现有组或创建新组
        affected_groups = set()

        for photo in new_photos:
            photo_time = photo.date_taken
            assigned_to_group = False

            # 尝试将照片分配到现有组
            for i, group in enumerate(self.timeline_groups):
                if group.can_add_photo(photo):
                    group.add_photo(photo)
                    self.photo_to_group_map[photo.uuid] = i
                    affected_groups.add(i)
                    assigned_to_group = True
                    logger.debug(f"📎 照片 {photo.filename} 添加到现有组 {i+1}")
                    break

            # 如果无法分配到现有组，创建新组
            if not assigned_to_group:
                new_group = TimelineGroup(photo_time, self.time_threshold_seconds)
                new_group.add_photo(photo)
                self.timeline_groups.append(new_group)
                group_index = len(self.timeline_groups) - 1
                self.photo_to_group_map[photo.uuid] = group_index
                affected_groups.add(group_index)
                logger.debug(f"🆕 照片 {photo.filename} 创建新组 {group_index+1}")

        # 3. 重新分析受影响的组
        logger.info(f"🔍 重新分析 {len(affected_groups)} 个受影响的时间线组")

        all_similarity_groups = []

        # 保留未受影响组的相似性结果
        for i, group in enumerate(self.timeline_groups):
            if i not in affected_groups and group.similarity_groups:
                all_similarity_groups.extend(group.similarity_groups)

        # 重新分析受影响的组
        for group_index in affected_groups:
            group = self.timeline_groups[group_index]
            logger.info(f"🔄 重新分析组 {group_index+1}: {len(group.photos)} 张照片")

            group_similarity_groups = self.analyze_timeline_group(group)
            all_similarity_groups.extend(group_similarity_groups)

        # 4. 更新全局照片列表
        self.all_photos.extend(new_photos)

        # 5. 停止hash计算
        self.stop_async_hash_calculation()

        total_time = time.time() - start_time

        result = {
            'similarity_groups': all_similarity_groups,
            'timeline_groups_count': len(self.timeline_groups),
            'affected_groups_count': len(affected_groups),
            'new_photos_count': len(new_photos),
            'total_photos': len(self.all_photos),
            'total_time_seconds': total_time,
            'performance_stats': {
                'timeline_groups': len(self.timeline_groups),
                'affected_groups': len(affected_groups),
                'avg_photos_per_group': len(self.all_photos) / len(self.timeline_groups) if self.timeline_groups else 0,
                'time_threshold_seconds': self.time_threshold_seconds
            }
        }

        logger.info(f"✅ 增量分析完成: 影响 {len(affected_groups)} 个组, "
                   f"总共 {len(all_similarity_groups)} 个相似组, 耗时 {total_time:.2f}秒")

        return result

    def get_photo_group_info(self, photo_uuid: str) -> Optional[Dict]:
        """
        获取照片所在的时间线组信息

        Args:
            photo_uuid: 照片UUID

        Returns:
            组信息字典或None
        """
        if photo_uuid not in self.photo_to_group_map:
            return None

        group_index = self.photo_to_group_map[photo_uuid]
        group = self.timeline_groups[group_index]

        return {
            'group_index': group_index,
            'group_start_time': group.start_time,
            'group_end_time': group.end_time,
            'photos_count': len(group.photos),
            'similarity_groups_count': len(group.similarity_groups)
        }


def analyze_photos_by_timeline(photos_input,
                             time_threshold_seconds: int = 300,
                             max_workers: int = 4,
                             progress_callback: Optional[Callable] = None) -> str:
    """
    基于时间线分析照片的便捷函数

    Args:
        photos_input: 照片列表或JSON字符串
        time_threshold_seconds: 时间线分组阈值（秒），默认300秒
        max_workers: 最大工作线程数
        progress_callback: 进度回调函数

    Returns:
        分析结果JSON字符串
    """
    import json

    # 处理输入：如果是字符串则解析JSON，否则直接使用
    if isinstance(photos_input, str):
        try:
            photos_data = json.loads(photos_input)
            photos = []

            for photo_dict in photos_data:
                # 处理位置信息：将latitude和longitude合并为location字典
                if 'latitude' in photo_dict and 'longitude' in photo_dict:
                    if photo_dict['latitude'] is not None and photo_dict['longitude'] is not None:
                        photo_dict['location'] = {
                            'latitude': photo_dict['latitude'],
                            'longitude': photo_dict['longitude']
                        }
                    # 移除单独的latitude和longitude字段
                    photo_dict.pop('latitude', None)
                    photo_dict.pop('longitude', None)

                # 处理date_taken：从时间戳转换为datetime对象，确保有时区信息
                if 'date_taken' in photo_dict and isinstance(photo_dict['date_taken'], (int, float)):
                    from datetime import datetime, timezone
                    photo_dict['date_taken'] = datetime.fromtimestamp(photo_dict['date_taken'], tz=timezone.utc)

                photos.append(PhotoInfo(**photo_dict))

        except Exception as e:
            logger.error(f"解析照片JSON数据失败: {e}")
            return json.dumps({"error": f"解析照片数据失败: {e}", "similarity_groups": []})
    else:
        photos = photos_input

    try:
        analyzer = TimelineAnalyzer(time_threshold_seconds, max_workers)
        result = analyzer.analyze_photos_by_timeline(photos, progress_callback)
        return json.dumps(result, default=str)
    except Exception as e:
        logger.error(f"时间线分析失败: {e}")
        return json.dumps({"error": f"时间线分析失败: {e}", "similarity_groups": []})


def add_photos_to_timeline_incrementally(existing_analyzer: TimelineAnalyzer,
                                        new_photos_input,
                                        progress_callback: Optional[Callable] = None) -> str:
    """
    向现有时间线分析器增量添加新照片的便捷函数

    Args:
        existing_analyzer: 现有的时间线分析器实例
        new_photos_input: 新照片列表或JSON字符串
        progress_callback: 进度回调函数

    Returns:
        分析结果JSON字符串
    """
    import json

    # 处理输入：如果是字符串则解析JSON，否则直接使用
    if isinstance(new_photos_input, str):
        try:
            photos_data = json.loads(new_photos_input)
            new_photos = []

            for photo_dict in photos_data:
                # 处理位置信息：将latitude和longitude合并为location字典
                if 'latitude' in photo_dict and 'longitude' in photo_dict:
                    if photo_dict['latitude'] is not None and photo_dict['longitude'] is not None:
                        photo_dict['location'] = {
                            'latitude': photo_dict['latitude'],
                            'longitude': photo_dict['longitude']
                        }
                    # 移除单独的latitude和longitude字段
                    photo_dict.pop('latitude', None)
                    photo_dict.pop('longitude', None)

                # 处理date_taken：从时间戳转换为datetime对象，确保有时区信息
                if 'date_taken' in photo_dict and isinstance(photo_dict['date_taken'], (int, float)):
                    from datetime import datetime, timezone
                    photo_dict['date_taken'] = datetime.fromtimestamp(photo_dict['date_taken'], tz=timezone.utc)

                new_photos.append(PhotoInfo(**photo_dict))

        except Exception as e:
            logger.error(f"解析新照片JSON数据失败: {e}")
            return json.dumps({"error": f"解析新照片数据失败: {e}", "similarity_groups": []})
    else:
        new_photos = new_photos_input

    try:
        result = existing_analyzer.add_photos_incrementally(new_photos)
        return json.dumps(result, default=str)
    except Exception as e:
        logger.error(f"增量时间线分析失败: {e}")
        return json.dumps({"error": f"增量时间线分析失败: {e}", "similarity_groups": []})

"""
Data models for photo deduplication and thumbnail management.
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Any, Callable, Dict, List, Optional


@dataclass
class PhotoInfo:
    """Represents a photo with all relevant metadata."""

    uuid: str
    filename: str
    original_path: str
    date_taken: datetime
    file_size: int
    width: int
    height: int
    mime_type: str

    # Optional fields
    thumbnail_path: Optional[str] = None
    hash_values: Optional[Dict[str, str]] = None
    camera_model: Optional[str] = None
    location: Optional[Dict[str, float]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        # Convert datetime to Unix timestamp for Rust compatibility
        date_taken_timestamp = int(self.date_taken.timestamp())

        # Handle location data - extract latitude and longitude if available
        latitude = None
        longitude = None
        if self.location:
            latitude = self.location.get("latitude")
            longitude = self.location.get("longitude")

        result = {
            "uuid": self.uuid,
            "filename": self.filename,
            "original_path": self.original_path,
            "date_taken": date_taken_timestamp,
            "file_size": self.file_size,
            "width": self.width,
            "height": self.height,
            "mime_type": self.mime_type,
            "thumbnail_path": self.thumbnail_path,
            "hash_values": self.hash_values,
            "camera_model": self.camera_model,
            "latitude": latitude,
            "longitude": longitude,
        }

        # 包含动态添加的hash属性（如果存在）
        # 只包含优化分析器实际使用的hash算法
        hash_attributes = ['ahash', 'phash']
        for attr in hash_attributes:
            if hasattr(self, attr):
                result[attr] = getattr(self, attr)

        return result


@dataclass
class ThumbnailInfo:
    """Represents a generated thumbnail."""

    photo_uuid: str
    thumbnail_path: str
    size: int
    quality: int
    width: int
    height: int
    created_at: datetime

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {"photo_uuid": self.photo_uuid, "thumbnail_path": self.thumbnail_path, "size": self.size, "quality": self.quality, "width": self.width, "height": self.height, "created_at": self.created_at.isoformat()}


@dataclass
class ThumbnailConfig:
    """Configuration for thumbnail generation."""

    output_dir: str
    size: str = "medium"  # small, medium, large
    quality: int = 85
    max_width: Optional[int] = None
    max_height: Optional[int] = None
    format: str = "JPEG"

    @property
    def dimensions(self) -> tuple[int, int]:
        """Get thumbnail dimensions based on size setting."""
        dimensions_map = {"small": (128, 128), "medium": (256, 256), "large": (512, 512)}
        return dimensions_map.get(self.size, (256, 256))


@dataclass
class SimilarityGroup:
    """Represents a group of similar photos."""

    group_id: str
    photos: List[PhotoInfo]
    similarity_score: float
    time_range: tuple[datetime, datetime]

    @property
    def photo_count(self) -> int:
        """Get number of photos in the group."""
        return len(self.photos)

    @property
    def duration_seconds(self) -> float:
        """Get duration of the group in seconds."""
        start, end = self.time_range
        return (end - start).total_seconds()

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        # 将时间范围转换为Unix时间戳，以匹配Rust结构体
        start_time = int(self.time_range[0].timestamp())
        end_time = int(self.time_range[1].timestamp())

        return {"group_id": self.group_id, "photos": [photo.to_dict() for photo in self.photos], "similarity_score": self.similarity_score, "start_time": start_time, "end_time": end_time, "photo_count": self.photo_count, "duration_seconds": self.duration_seconds}


ProgressCallback = Optional[Callable[[str, int, int, str], None]]
